# 通用软件注册机（License Generator）

## 项目概括
本项目旨在开发一个基于 WPF 和 .NET 9 的桌面应用程序，用于生成、验证和管理各种软件许可证。采用 MVVM 架构模式，提供直观的用户界面和流畅的用户体验。该工具支持完全自定义的许可证类型，具备强大的加密保护机制，可满足不同软件产品的授权需求。

## 技术选型
- **开发框架**: WPF (Windows Presentation Foundation)
- **.NET 版本**: .NET 9.0
- **架构模式**: MVVM (Model-View-ViewModel)
- **UI框架**: 原生 WPF 控件（不使用第三方 UI 库）
- **加密算法**: RSA、AES、SHA256 等多种加密算法
- **数据存储**: SQLite（本地数据库）+ JSON/XML（配置文件）
- **依赖注入**: Microsoft.Extensions.DependencyInjection
- **消息传递**: CommunityToolkit.Mvvm
- **序列化**: System.Text.Json
- **版本控制**: Git
- **其他工具**: NUnit (测试), Serilog (日志), AutoMapper (对象映射)

## WPF 项目结构 / 模块划分
- `/Views/`: XAML 视图文件
  - `MainWindow.xaml`: 主窗口
  - `LicenseGeneratorView.xaml`: 许可证生成界面
  - `LicenseValidatorView.xaml`: 许可证验证界面
  - `ConfigurationView.xaml`: 自定义配置界面
  - `LicenseManagementView.xaml`: 许可证管理界面
  - `SettingsView.xaml`: 系统设置界面
- `/ViewModels/`: 视图模型类
  - `MainViewModel.cs`: 主窗口视图模型
  - `LicenseGeneratorViewModel.cs`: 许可证生成视图模型
  - `LicenseValidatorViewModel.cs`: 许可证验证视图模型
  - `ConfigurationViewModel.cs`: 配置管理视图模型
  - `LicenseManagementViewModel.cs`: 许可证管理视图模型
  - `SettingsViewModel.cs`: 设置视图模型
- `/Models/`: 数据模型类
  - `LicenseInfo.cs`: 许可证信息模型
  - `LicenseTemplate.cs`: 许可证模板模型
  - `UserInfo.cs`: 用户信息模型
  - `ConfigurationModel.cs`: 配置模型
- `/Services/`: 业务服务层
  - `LicenseGeneratorService.cs`: 许可证生成服务
  - `LicenseValidatorService.cs`: 许可证验证服务
  - `EncryptionService.cs`: 加密服务
  - `ConfigurationService.cs`: 配置管理服务
  - `DatabaseService.cs`: 数据库服务
- `/Converters/`: 值转换器
  - `BoolToVisibilityConverter.cs`: 布尔值到可见性转换器
  - `DateTimeConverter.cs`: 日期时间转换器
  - `StatusToColorConverter.cs`: 状态到颜色转换器
- `/Controls/`: 自定义用户控件
  - `LicenseInfoControl.xaml`: 许可证信息展示控件
  - `EncryptionSettingsControl.xaml`: 加密设置控件
- `/Resources/`: 资源文件
  - `/Styles/`: 样式文件
    - `ButtonStyles.xaml`: 按钮样式
    - `TextBoxStyles.xaml`: 文本框样式
    - `DataGridStyles.xaml`: 数据网格样式
  - `/Templates/`: 模板文件
    - `WindowTemplate.xaml`: 窗口模板
    - `ControlTemplates.xaml`: 控件模板
  - `/Images/`: 图片资源
- `/Data/`: 数据访问层
  - `LicenseRepository.cs`: 许可证数据仓储
  - `ConfigurationRepository.cs`: 配置数据仓储
- `/Utils/`: 工具类和辅助函数
  - `CryptoHelper.cs`: 加密辅助类
  - `FileHelper.cs`: 文件操作辅助类
  - `ValidationHelper.cs`: 验证辅助类
- `App.xaml`: 应用程序定义
- `MainWindow.xaml`: 主窗口
- `LicenseGenerator.csproj`: 项目文件

## 核心功能模块 / 界面详解
- **许可证生成模块**: 提供灵活的许可证生成功能，支持自定义许可证参数（有效期、功能限制、用户信息等），支持批量生成和多种加密算法保护。
- **许可证验证模块**: 提供离线许可证验证功能，检查许可证的有效性、完整性和是否过期，支持多种验证策略和防篡改机制。
- **自定义授权配置模块**: 允许用户自定义许可证类型和授权规则，提供可视化的配置界面，支持模板的导入导出功能。
- **许可证管理模块**: 提供许可证的全生命周期管理，包括增删改查、使用记录统计、状态监控和批量操作功能。
- **系统设置模块**: 提供应用程序配置管理，包括加密算法选择、界面主题设置、数据库配置等系统级设置。

## 数据模型设计
- **LicenseInfo**: { Id (Guid, PK), ProductName (string), UserName (string), CompanyName (string), Email (string), LicenseKey (string), ExpirationDate (DateTime?), Features (List<string>), IsActive (bool), CreatedAt (DateTime), UpdatedAt (DateTime) }
- **LicenseTemplate**: { Id (Guid, PK), TemplateName (string), Description (string), DefaultExpiration (int), AllowedFeatures (List<string>), EncryptionType (string), IsDefault (bool), CreatedAt (DateTime) }
- **UserInfo**: { Id (Guid, PK), Name (string), Company (string), Email (string), Phone (string), Address (string), CreatedAt (DateTime) }
- **ConfigurationModel**: { Id (Guid, PK), Key (string), Value (string), Category (string), Description (string), IsSystem (bool) }

## WPF 架构设计
- **View层**: 负责用户界面展示，使用XAML定义界面布局和样式，采用现代化的扁平设计风格
- **ViewModel层**: 作为View和Model之间的桥梁，处理界面逻辑和数据绑定，实现命令模式处理用户交互
- **Model层**: 定义业务数据模型和业务逻辑，包括许可证实体、配置模型等
- **Service层**: 提供数据访问、业务服务等功能，包括加密服务、数据库服务、配置服务等
- **依赖注入**: 使用Microsoft.Extensions.DependencyInjection管理对象生命周期和依赖关系
- **数据访问层**: 使用Repository模式封装数据访问逻辑，支持SQLite本地存储

## 界面设计规范
- **主题风格**: 现代化扁平设计，支持浅色和深色主题切换
- **色彩方案**:
  - 主色调: #2196F3 (蓝色)
  - 辅助色: #FFC107 (琥珀色)
  - 强调色: #F44336 (红色，用于警告和错误)
  - 背景色: #FAFAFA (浅色主题) / #303030 (深色主题)
- **字体规范**:
  - 标题: Microsoft YaHei UI, 16px, Bold
  - 正文: Microsoft YaHei UI, 12px, Regular
  - 按钮: Microsoft YaHei UI, 12px, Medium
- **控件样式**:
  - 按钮: 圆角矩形，悬停效果，点击动画
  - 文本框: 扁平边框，聚焦高亮，验证状态指示
  - 数据网格: 斑马纹行，排序指示器，选中高亮
- **布局原则**:
  - 采用Grid和StackPanel组合布局
  - 响应式设计，支持窗口大小调整
  - 合理的间距和对齐方式

## 技术实现细节
[本部分初始为空。在后续开发每一个模块/功能时，AI 会自动将该模块/功能的MVVM实现方案、XAML界面设计、数据绑定策略、关键代码片段说明等填充至此。]

## 开发状态跟踪
| 功能模块/界面          | View状态 | ViewModel状态 | 负责人 | 计划完成日期 | 实际完成日期 | 备注与链接 |
|----------------------|----------|---------------|--------|--------------|--------------|-----------|
| 主窗口界面            | 未开始   | 未开始        | AI     | 2024-12-15   |              |           |
| 许可证生成模块        | 未开始   | 未开始        | AI     | 2024-12-16   |              |           |
| 许可证验证模块        | 未开始   | 未开始        | AI     | 2024-12-17   |              |           |
| 自定义授权配置模块    | 未开始   | 未开始        | AI     | 2024-12-18   |              |           |
| 许可证管理模块        | 未开始   | 未开始        | AI     | 2024-12-19   |              |           |
| 系统设置模块          | 未开始   | 未开始        | AI     | 2024-12-20   |              |           |
| 加密服务实现          | 未开始   | 未开始        | AI     | 2024-12-21   |              |           |
| 数据库服务实现        | 未开始   | 未开始        | AI     | 2024-12-22   |              |           |
| 样式和模板设计        | 未开始   | 未开始        | AI     | 2024-12-23   |              |           |

## 代码检查与问题记录
[本部分用于记录WPF代码检查结果和开发过程中遇到的问题及其解决方案，包括XAML验证、数据绑定问题、性能优化等。]

## 环境设置与运行指南
### 开发环境要求
- **操作系统**: Windows 10/11 (x64)
- **开发工具**: Visual Studio 2022 (17.8+) 或 Visual Studio Code
- **.NET SDK**: .NET 9.0 SDK
- **数据库**: SQLite (无需额外安装)

### 项目依赖包
```xml
<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
<PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
<PackageReference Include="System.Data.SQLite" Version="1.0.118" />
<PackageReference Include="Serilog" Version="3.1.1" />
<PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
<PackageReference Include="AutoMapper" Version="12.0.1" />
<PackageReference Include="NUnit" Version="3.14.0" />
<PackageReference Include="NUnit3TestAdapter" Version="4.5.0" />
```

### 运行步骤
1. 克隆项目到本地
2. 使用 Visual Studio 打开 `LicenseGenerator.sln`
3. 还原 NuGet 包: `dotnet restore`
4. 编译项目: `dotnet build`
5. 运行项目: `dotnet run` 或按 F5

### 调试命令
- 编译: `dotnet build --configuration Debug`
- 发布: `dotnet publish --configuration Release --output ./publish`
- 测试: `dotnet test`

## WPF性能优化
### 性能优化策略
- **UI虚拟化**: 对大数据量的ListBox、DataGrid启用虚拟化
- **数据绑定优化**: 使用OneWay绑定替代TwoWay绑定（适用场景）
- **资源管理**: 及时释放不再使用的资源和事件订阅
- **异步操作**: 耗时操作使用async/await避免UI阻塞
- **内存管理**: 避免内存泄漏，正确处理事件订阅和取消订阅

### 性能监控指标
- 应用启动时间 < 3秒
- 界面响应时间 < 200ms
- 内存使用量 < 100MB（正常使用）
- CPU使用率 < 10%（空闲状态）

## 部署指南
### 发布配置
- **目标框架**: .NET 9.0
- **部署模式**: Framework-dependent deployment (FDD)
- **平台**: Windows x64
- **输出类型**: Windows Application (.exe)

### 安装包制作
- 使用 WiX Toolset 或 Inno Setup 制作安装程序
- 包含 .NET 9.0 Runtime 检测和自动安装
- 支持静默安装和卸载
- 创建桌面快捷方式和开始菜单项

### 系统要求
- **操作系统**: Windows 10 版本 1809 或更高版本
- **内存**: 最少 2GB RAM
- **存储空间**: 最少 100MB 可用空间
- **.NET Runtime**: .NET 9.0 Desktop Runtime